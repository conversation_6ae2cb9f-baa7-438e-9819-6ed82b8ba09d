# SpeakMCP Build Guide

## Prerequisites

### 1. Environment Setup
Ensure you have a `.env` file in the project root with the following variables:

```bash
# Apple Developer credentials for code signing and notarization
APPLE_TEAM_ID=6K8L5F5PA8
APPLE_ID=<EMAIL>
APPLE_APP_SPECIFIC_PASSWORD=brhx-mevz-kvxh-picy

# Code signing identity (for regular distribution)
CSC_NAME="Developer ID Application: <PERSON><PERSON> (6K8L5F5PA8)"

# Mac App Store signing identities
CSC_MAS_NAME="99C3BCDDDCEFBDE9CE7FA1B3B38A4DE60B75052C"
CSC_INSTALLER_LINK="7CCC466DE6C7804C843ADF746EA9FB3996B3A362"

# Provisioning profiles (if needed)
MAS_PROVISIONING_PROFILE="~/Library/MobileDevice/Provisioning Profiles/SpeakMCP_Mac_App_Store.provisionprofile"
```

### 2. Certificates Required
- **Developer ID Application**: For regular distribution outside Mac App Store
- **3rd Party Mac Developer Application**: For Mac App Store builds
- **3rd Party Mac Developer Installer**: For Mac App Store installer packages

## Build Commands

### ✅ Working Builds

#### Development Build
```bash
npm run build        # Compiles TypeScript and builds all components
npm run start        # Runs the app in development mode
```

#### Distribution Builds (Outside Mac App Store)
```bash
npm run build:mac           # Creates signed DMG and PKG for distribution
npm run build:mac:signed    # Creates signed build without publishing
npm run build:mac:universal # Creates universal (x64 + arm64) build
```

#### Platform-Specific Builds
```bash
npm run build:win    # Windows build
npm run build:linux  # Linux build
```

### ⚠️ Mac App Store Build (Has Runtime Issues)

```bash
npm run build:mas    # Creates MAS app (builds successfully but crashes on launch)
```

**Status**: The build process completes successfully and creates a properly signed MAS app, but the app crashes on launch due to Electron's Mach port system conflicting with MAS sandbox restrictions.

## Testing

### Regular Build Testing
```bash
# Test development build
npm run start

# Test packaged build stability
node test-launch.js
```

### Mac App Store Build Testing
```bash
# Test MAS build (will show crash details)
node test-mas-simple.js
```

## Build Process Details

### 1. TypeScript Compilation
- Main process: `src/main/` → `out/main/`
- Preload scripts: `src/preload/` → `out/preload/`
- Renderer: `src/renderer/` → `out/renderer/`

### 2. Rust Binary Build
- Builds `speakmcp-rs` binary
- Copies to `resources/bin/speakmcp-rs`
- Signs binary on macOS

### 3. Electron Packaging
- Uses electron-builder configuration
- Applies appropriate code signing
- Creates distribution packages

## Troubleshooting

### Code Signing Issues
**Problem**: "Identity name is specified, but no valid identity with this name in the keychain"

**Solution**: 
1. Ensure certificates are installed in Keychain Access
2. Verify `.env` file contains correct certificate names/hashes
3. Build scripts now automatically load `.env` file

### Mac App Store Crash
**Problem**: App crashes with Mach port rendezvous error

**Current Status**: Known issue with Electron in MAS sandbox
- App builds and signs correctly
- Crashes immediately on launch due to IPC restrictions
- Requires additional Electron configuration or alternative IPC mechanism

### Build Script Failures
**Problem**: Environment variables not loaded

**Solution**: All build scripts now include `source .env 2>/dev/null || true &&` to automatically load environment variables.

## File Structure

```
dist/
├── mac/                    # Regular macOS build
├── mas-arm64/             # Mac App Store build (ARM64)
├── SpeakMCP-*.dmg         # Distribution DMG files
├── SpeakMCP-*.pkg         # Distribution PKG files
└── SpeakMCP-*-mas.pkg     # Mac App Store package (if installer succeeds)
```

## Next Steps for MAS Compatibility

1. **Electron Version Upgrade**: Test with latest Electron version
2. **Alternative IPC**: Implement MAS-compatible IPC mechanism
3. **Feature Conditional Logic**: Disable problematic features in MAS builds
4. **Electron Forge Migration**: Consider migrating to Electron Forge for better MAS support

## Success Criteria

- ✅ Regular builds: Complete successfully and launch without crashes
- ✅ Code signing: All builds are properly signed with correct certificates
- ✅ Distribution: DMG and PKG files are created and notarized
- ⚠️ Mac App Store: Builds successfully but requires runtime compatibility fixes
